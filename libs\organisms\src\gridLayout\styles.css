.background-circle {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #1C0B45, #2E1065);
  overflow: hidden;
}

.background-circle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, rgba(255,255,255,0.05) 7px, transparent 7px);
  background-size: 40px 40px;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .background-circle {
    min-height: 50vh;
  }
}
