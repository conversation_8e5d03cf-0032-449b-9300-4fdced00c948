{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "files": [], "include": ["src/**/*.tsx", "src/**/*.ts", "src/**/*.jsx", "src/**/*.js", "**/*.stories.tsx", "**/*.stories.ts", "**/*.stories.jsx", "**/*.stories.js", "**/*.stories.mdx", "src/**/*.stories.tsx", "src/**/*.stories.ts"], "exclude": ["jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}