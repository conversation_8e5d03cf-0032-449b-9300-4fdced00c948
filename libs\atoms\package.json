{"name": "@social-media/atoms", "version": "0.0.1", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "default": "./src/index.ts"}, "./package.json": "./package.json"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.510.0", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.2.9"}}