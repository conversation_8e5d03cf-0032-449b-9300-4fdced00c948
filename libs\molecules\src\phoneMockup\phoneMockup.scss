// libs/molecules/src/phoneMockup/PhoneMockup.scss

.phone-mockup {
  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  &-controls {
    width: 100%;
    max-width: 400px;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .control-group {
      &.control-group-colors {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
      }

      .control-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
        text-align: center;
      }

      .control-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: white;
        font-size: 0.875rem;
        text-align: center;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        &:hover {
          border-color: #9ca3af;
        }
      }

      .control-color-item {
        .control-color {
          width: 100%;
          height: 3rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          cursor: pointer;
          transition: all 0.2s ease;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }

          &:hover {
            border-color: #9ca3af;
          }
        }
      }

      .control-orientation-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        width: 100%;
        padding: 0.75rem 1rem;
        background-color: #f9fafb;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;

        &:hover {
          background-color: #f3f4f6;
          border-color: #9ca3af;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0) scale(0.98);
        }

        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .control-rotation-icon {
          width: 16px;
          height: 16px;
          transition: transform 0.3s ease;

          &.landscape {
            transform: rotate(90deg);
          }
        }
      }
    }
  }

  &-wrapper {
    position: relative;
    margin: 20px auto;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  &-outer-container {
    position: relative;
    transition: all 0.3s ease;
    overflow: visible;
    // margin: 20px; // Removed margin, moved to wrapper
  }

  &-side-button {
    position: absolute;
    background-color: #d1d5db;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &-inner-container {
    position: absolute;
    background-color: #000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-notch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000000;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .speaker {
      width: 35%;
      height: 4px;
      background-color: #333;
      border-radius: 2px;
    }

    .camera {
      width: 10px;
      height: 10px;
      background-color: #333;
      border-radius: 50%;
      border: 1px solid #444;
    }
  }

  &-teardrop-notch {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .camera {
      width: 8px;
      height: 8px;
      background-color: #333;
      border-radius: 50%;
      border: 1px solid #444;
    }
  }

  &-camera-cutout {
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    z-index: 10;

    .camera {
      width: 10px;
      height: 10px;
      background-color: #333;
      border-radius: 50%;
      border: 1px solid #444;
    }

    .flash {
      width: 6px;
      height: 6px;
      background-color: #777;
      border-radius: 50%;
    }
  }

  &-status-bar {
    width: 100%;
    z-index: 5;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    .status-left {
      .time {
        color: #fff;
        opacity: 0.9;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }

    .status-right {
      display: flex;
      align-items: center;

      .carrier {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        letter-spacing: 0.3px;
      }

      .battery-percentage {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        min-width: 24px;
        text-align: right;
      }

      .signal-bars {
        display: flex;
        align-items: flex-end;

        .bar {
          background-color: rgba(255, 255, 255, 0.5);
          transition: background-color 0.3s ease;

          &.strong {
            background-color: rgba(255, 255, 255, 0.9);
          }
        }
      }

      .wifi-icon {
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;

        &.cellular {
          color: rgba(255, 255, 255, 0.7);
        }

        svg {
          width: 100%;
          height: 100%;
        }

        .cellular-bars {
          display: flex;
          align-items: flex-end;
          gap: 1px;
          width: 100%;
          height: 100%;

          .cellular-bar {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 0.5px;
            width: 2px;

            &:nth-child(1) {
              height: 25%;
            }

            &:nth-child(2) {
              height: 50%;
            }

            &:nth-child(3) {
              height: 75%;
            }

            &:nth-child(4) {
              height: 100%;
            }
          }
        }
      }

      .battery {
        position: relative;
        background-color: transparent;
        border: 0.5px solid rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;

        &.charging {
          border-color: rgba(0, 255, 68, 0.9);

          .battery-level {
            animation: chargingPulse 2s ease-in-out infinite;
          }
        }

        .battery-level {
          background-color: rgba(255, 255, 255, 0.9);
          height: 100%;
          transition: width 0.3s ease;

          &.low {
            background-color: #ff4444;
          }

          &.medium {
            background-color: #ffaa00;
          }

          &.high {
            background-color: rgba(255, 255, 255, 0.9);
          }

          &.full {
            background-color: #00ff44;
          }
        }

        .charging-indicator {
          position: absolute;
          color: #00ff44;
          z-index: 1;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          animation: chargingBlink 1s ease-in-out infinite;
        }

        .battery-tip {
          position: absolute;
          background-color: rgba(255, 255, 255, 0.9);
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }

  &-content {
    &-outer-container {
      flex: 1;
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      background-color: #f5f5f5;
      overflow: auto;
      width: 100%;
      box-sizing: border-box;
      min-height: 100%;
    }

    &-width-limiter {
      box-sizing: border-box;
      margin: 0 auto;
      width: 100%;
      height: 100%;
      min-height: 100%;
    }

    &-wrapper {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      min-height: 100%;
    }
  }

  &-home-button {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: transparent;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    z-index: 10;
  }

  &-ios-home-indicator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
  }

  &-navigation-bar {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;

    .nav-buttons {
      display: flex;
      gap: 40px;

      .back,
      .home,
      .recent {
        width: 14px;
        height: 14px;
        background-color: #fff;
        border-radius: 50%;
      }

      .back {
        clip-path: polygon(60% 0%, 60% 100%, 0% 50%);
      }

      .home {
        border-radius: 3px;
      }

      .recent {
        border: 2px solid #fff;
        background-color: transparent;
      }
    }
  }

  &-fold-crease {
    position: absolute;
    left: 50%;
    width: 1px;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0.1));
    z-index: 15;
    transform: translateX(-50%);
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -2px;
      width: 5px;
      height: 100%;
      background: linear-gradient(to right,
          rgba(0, 0, 0, 0.1) 0%,
          rgba(255, 255, 255, 0.1) 30%,
          rgba(255, 255, 255, 0.3) 50%,
          rgba(255, 255, 255, 0.1) 70%,
          rgba(0, 0, 0, 0.1) 100%);
      opacity: 0.4;
    }
  }

  &-folded-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;

    &:before,
    &:after {
      content: '';
      position: absolute;
      top: 0;
      height: 100%;
      width: 50%;
      background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05));
      pointer-events: none;
    }

    &:before {
      left: 0;
      z-index: 5;
    }

    &:after {
      right: 0;
      transform: scaleX(-1);
      z-index: 5;
    }
  }
}

// Default content styles
.phone-default-content {
  padding: 20px;
  text-align: center;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #f8f9fa;

  .phone-default-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .phone-default-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .phone-default-subtitle {
    font-size: 12px;
    color: #666;
  }
}

// Animations
@keyframes chargingPulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes chargingBlink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0.3;
  }
}

// Responsive
@media (max-width: 640px) {
  .phone-mockup-controls {
    width: 100%;
    max-width: none;
  }
}