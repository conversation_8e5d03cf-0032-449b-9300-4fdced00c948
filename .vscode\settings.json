{"nxConsole.nxWorkspacePath": "", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.requireConfig": true, "prettier.useEditorConfig": false, "nxConsole.generateAiAgentRules": true}