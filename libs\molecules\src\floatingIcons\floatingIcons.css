/* Basic styling for FloatingIcons component */

.floating-icons-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  overflow: hidden;
}

.floating-icon {
  position: absolute;
  will-change: transform;
  transition: all 0.3s ease-out;
}

/* Add a subtle hover effect for interactive feeling */
.floating-icon:hover {
  transform: scale(1.1);
  z-index: 10;
}

/* Position classes */
.floating-icons-foreground {
  z-index: 10;
  pointer-events: none; /* Allow clicks to pass through to underlying elements */
}

.floating-icons-foreground .floating-icon {
  pointer-events: auto; /* Re-enable pointer events for the icons themselves */
}

.floating-icons-background {
  z-index: 0;
  pointer-events: none;
}

/* Additional styling for background floating icons */
.floating-icons-background .floating-icon img {
  filter: blur(1px);
}
