// S<PERSON> dụng cấu hình <PERSON>wind từ thư mục root
const rootTailwindConfig = require('../../tailwind.config.js');

/** @type {import('tailwindcss').Config} */
module.exports = {
  // <PERSON><PERSON> thừa tất cả từ cấu hình root
  ...rootTailwindConfig,
  // Tùy chỉnh nội dung cho thư viện này nếu cần
  content: [
    './src/**/*.{ts,tsx,js,jsx,html}',
    './src/**/*.{stories,spec}.{ts,tsx,js,jsx,html}',
    '../../libs/atoms/src/**/*.{ts,tsx,js,jsx}',
    '../../libs/molecules/src/**/*.{ts,tsx,js,jsx}',
    ...rootTailwindConfig.content,
  ],
};
