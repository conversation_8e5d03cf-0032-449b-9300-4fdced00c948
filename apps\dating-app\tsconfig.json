{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "jsx": "preserve",
    "noEmit": true,
    "emitDeclarationOnly": false,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["jest", "node"],
    "outDir": "dist",
    "tsBuildInfoFile": "dist/tsconfig.tsbuildinfo"
  },
  "include": [
    "../../apps/dating-app/.next/types/**/*.ts",
    "../../dist/apps/dating-app/.next/types/**/*.ts",
    "next-env.d.ts",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/**/*.ts",
    "src/**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "out-tsc",
    "dist",
    "node_modules",
    "jest.config.ts",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    ".next",
    "eslint.config.js",
    "eslint.config.cjs",
    "eslint.config.mjs",
    "**/*.stories.ts",
    "**/*.stories.js"
  ],
}
