{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["**/*.stories.tsx", "**/*.stories.ts", "**/*.stories.jsx", "**/*.stories.js", "**/*.stories.mdx", "../../libs/atoms/src/**/*.{ts,tsx}", "../../libs/molecules/src/**/*.{ts,tsx}"], "exclude": ["jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}