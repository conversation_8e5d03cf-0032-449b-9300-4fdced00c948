@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 5.9% 98%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

*,
*:before,
*:after {
  box-sizing: border-box;
}


* {
  margin: 0;
  padding: 0;
  font: inherit;
}

img,
picture,
svg,
video {
  display: block;
  max-width: 100%;
}

input,
select,
textarea {
  background-color: transparent;
  outline: none;
}

button {
  cursor: pointer;
  background-color: transparent;
  outline: none;
  border: 0;
}

body {
  min-height: 100vh;
  font-weight: 400;
  font-size: 16px;
  line-height: 1;
}

/* Gradient Background Classes */
.bg-primary-gradient-to-right {
  background-image: linear-gradient(to right, #ff6b6b, #ff9e80);
}
.bg-primary-gradient-to-left {
  background-image: linear-gradient(to left, #ff6b6b, #ff9e80);
}
.bg-primary-gradient-to-top {
  background-image: linear-gradient(to top, #ff6b6b, #ff9e80);
}
.bg-primary-gradient-to-bottom {
  background-image: linear-gradient(to bottom, #ff6b6b, #ff9e80);
}

.bg-secondary-blue-gradient {
  background-image: linear-gradient(to right, #2e86de, #54a0ff);
}

.bg-secondary-green-gradient {
  background-image: linear-gradient(to right, #20bf6b, #26de81);
}

.bg-secondary-gold-gradient {
  background-image: linear-gradient(to right, #fed330, #f7b731);
}

.bg-secondary-purple-gradient {
  background-image: linear-gradient(to right, #9c27b0, #d500f9);
}

.bg-primary-purple-gradient {
  background-image: linear-gradient(135deg, #2e1065, #2e1065);
}
/* Text Gradient Classes */
.text-primary-gradient {
  background-image: linear-gradient(to right, #ff6b6b, #ff9e80);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-secondary-blue-gradient {
  background-image: linear-gradient(to right, #2e86de, #54a0ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-secondary-green-gradient {
  background-image: linear-gradient(to right, #20bf6b, #26de81);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-secondary-gold-gradient {
  background-image: linear-gradient(to right, #fed330, #f7b731);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-secondary-purple-gradient {
  background-image: linear-gradient(to right, #9c27b0, #d500f9);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Border Gradient Classes */
.border-primary-gradient {
  border: 2px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, #ff6b6b, #ff9e80);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.border-secondary-blue-gradient {
  border: 2px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, #2e86de, #54a0ff);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.border-secondary-green-gradient {
  border: 2px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, #20bf6b, #26de81);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.border-secondary-gold-gradient {
  border: 2px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, #fed330, #f7b731);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.border-secondary-purple-gradient {
  border: 2px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, #9c27b0, #d500f9);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}
